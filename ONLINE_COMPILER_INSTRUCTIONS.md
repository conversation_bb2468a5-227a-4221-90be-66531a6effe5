# Online Compiler Instructions

If you don't have Visual Studio or MinGW installed, you can use an online compiler to build the mod.

## 🌐 Recommended Online Compilers

1. **Replit** (https://replit.com)
   - Create account (free)
   - Create new C++ project
   - Paste the code
   - Click "Run"

2. **OnlineGDB** (https://www.onlinegdb.com/online_c++_compiler)
   - No account needed
   - Select C++ language
   - Paste the code
   - Click "Run"

3. **Compiler Explorer** (https://godbolt.org/)
   - Advanced compiler with multiple options
   - Good for testing different compilers

## 📋 Steps for Online Compilation

### Step 1: Copy the Code
1. Open `BO3_Mods.cpp` in a text editor
2. Select all (Ctrl+A) and copy (Ctrl+C)

### Step 2: Use Online Compiler
1. Go to https://replit.com or https://www.onlinegdb.com
2. Create new C++ project
3. Delete default code
4. Paste our code (Ctrl+V)
5. Click "Run" or "Compile"

### Step 3: Download Executable
1. If compilation succeeds, download the executable
2. Rename it to `BO3_ZombiesMod.exe`
3. Transfer to your gaming PC

## ⚠️ Important Notes

- **Online compilers** may not produce Windows executables
- **Linux executables** won't work on Windows
- **Best option** is still installing Visual Studio locally

## 🔧 Local Installation (Recommended)

### Option 1: Visual Studio Community (Free)
1. Download from: https://visualstudio.microsoft.com/vs/community/
2. Install with "Desktop development with C++" workload
3. Use our `compile.bat` script

### Option 2: MinGW-w64 (Lightweight)
1. Download from: https://www.mingw-w64.org/downloads/
2. Or install MSYS2: https://www.msys2.org/
3. Add to PATH environment variable
4. Use our `compile_mingw.bat` script

### Option 3: Build Tools Only
1. Download "Build Tools for Visual Studio"
2. Install C++ build tools
3. Use `compile.bat`

## 🎯 Quick Test

To test if your compiler works:

```cpp
#include <iostream>
int main() {
    std::cout << "Compiler works!" << std::endl;
    return 0;
}
```

If this compiles and runs, you're ready for the full mod!

## 📞 Need Help?

If you're having trouble:
1. Try the MinGW option (easier than Visual Studio)
2. Use an online compiler for testing
3. Ask a friend with Visual Studio to compile for you
4. Check YouTube for "How to install MinGW Windows"

The mod is ready to go - you just need a C++ compiler!
