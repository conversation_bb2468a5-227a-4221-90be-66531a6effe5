@echo off
title BO3 Game Pass GSC Injector - Easy Mode
color 0A

echo ========================================
echo    BO3 GAME PASS GSC INJECTOR
echo         EASY MODE
echo ========================================
echo.
echo INSTRUCTIONS:
echo 1. Start Black Ops 3 (Game Pass)
echo 2. Go to MAIN MENU (don't load into match yet)
echo 3. Press ANY KEY when you're in main menu
echo 4. <PERSON><PERSON><PERSON> will auto-inject icebreaker menu
echo 5. Then load into zombies match
echo 6. Press GRENADE + MELEE to open menu
echo.
echo ========================================
echo.

:WAIT_FOR_USER
echo Waiting for you to be in BO3 main menu...
echo Press ANY KEY when you're ready for injection...
pause >nul

echo.
echo ========================================
echo INJECTING ICEBREAKER MENU...
echo ========================================
echo.

REM Check if BO3 is running
tasklist /FI "IMAGENAME eq BlackOps3.exe" 2>NUL | find /I /N "BlackOps3.exe">NUL
if "%ERRORLEVEL%"=="1" (
    echo ERROR: Black Ops 3 is not running!
    echo Please start BO3 and get to main menu first.
    echo.
    goto WAIT_FOR_USER
)

echo Black Ops 3 detected! Injecting menu...
echo.

REM Run the Game Pass GSC Injector
"GamePass_GSC_Injector.exe" "icebreaker.gsc"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo    INJECTION SUCCESSFUL!
    echo ========================================
    echo.
    echo NEXT STEPS:
    echo 1. Go to Zombies mode in BO3
    echo 2. Load into any map
    echo 3. Press GRENADE + MELEE to open menu
    echo 4. Use AIM/SHOOT to scroll, F to select
    echo.
    echo FEATURES AVAILABLE:
    echo - Rapid Fire (perk_weapRateMultiplier)
    echo - Super Jump (Moon Jump/bg_gravity)
    echo - Godmode, Infinite Ammo, and more!
    echo.
    echo ========================================
) else (
    echo.
    echo ========================================
    echo    INJECTION FAILED!
    echo ========================================
    echo.
    echo TROUBLESHOOTING:
    echo - Make sure you're in MAIN MENU (not in-game)
    echo - Try running this script as Administrator
    echo - Make sure BO3 is the Game Pass version
    echo.
    echo Press ANY KEY to try again...
    pause >nul
    goto WAIT_FOR_USER
)

echo.
echo Press ANY KEY to close this window...
pause >nul
