#include <Windows.h>
#include <iostream>
#include <map>
#include <vector>
#include <string>
#include <TlHelp32.h>

// Forward declarations
struct Vec3;
struct CEntity;
struct CG_WeaponInfo;
struct CClientInfo;
struct CG_T;

// Global variables
HANDLE g_hProcess = nullptr;
DWORD g_processId = 0;

// Memory access functions
template<typename T>
T ReadMemory(uintptr_t address) {
    T value = {};
    if (g_hProcess) {
        ReadProcessMemory(g_hProcess, (LPCVOID)address, &value, sizeof(T), nullptr);
    }
    return value;
}

template<typename T>
bool WriteMemory(uintptr_t address, T value) {
    if (g_hProcess) {
        return WriteProcessMemory(g_hProcess, (LPVOID)address, &value, sizeof(T), nullptr);
    }
    return false;
}

// Data structures
struct Vec3 {
    float x, y, z;
    
    Vec3() : x(0), y(0), z(0) {}
    Vec3(float _x, float _y, float _z) : x(_x), y(_y), z(_z) {}
};

struct CEntity {
    char unknown0[64];          // 0x00
    Vec3 vOrigin;              // 0x40 - Position
    Vec3 vAngles;              // 0x4C - View angles
    char unknown1[0x548];       // 0x58 - Gap to flags
    int32_t flags1;            // 0x5A0 - First flag set
    char unknown2[0xD4];        // 0x5A4 - Gap
    int32_t flags2;            // 0x678 - Second flag set
    char unknown3[0x194];       // 0x67C - Gap
    BYTE Type;                 // 0x810
    char unknown4[0xE4];        // Gap to IsAlive
    BYTE IsAlive;              // 0x8F4
};

struct CG_WeaponInfo {
    char unknown0[0x10];
    float damage;              // 0x10 - Base damage
    float fireDelay;           // 0x14 - Time between shots
    float reloadTime;          // 0x18 - Reload duration
    float adsTime;             // 0x1C - Aim down sight time
    float sprintOutTime;       // 0x20 - Time to ready weapon after sprint
    float hipSpread;           // 0x24 - Hip fire accuracy
    float adsSpread;           // 0x28 - ADS accuracy
    float recoilKick;          // 0x2C - Vertical recoil
    float recoilSide;          // 0x30 - Horizontal recoil
    int maxAmmoClip;           // 0x34 - Clip size
    int maxAmmoReserve;        // 0x38 - Reserve ammo
    float range;               // 0x3C - Damage falloff range
    float penetration;         // 0x40 - Wall penetration
    float explosionRadius;     // 0x44 - For explosive weapons
    float explosionDamage;     // 0x48 - Explosion damage
};

struct CClientInfo {
    char unknown0[12];         // 0x00
    char szName[16];           // 0x0C
    char unknown1[16];         // 0x1C
    int iTeam;                 // 0x2C
    char unknown2[0x6CC];      // Gap to weapon index
    WORD WpnIndex;             // 0x6F8
};

struct CG_T {
    int LocalIndex;            // 0x00 - Local player index
    // ... more data
};

// Game function addresses (BO3 Version ********)
#define CG_GetEntity        0x140032530
#define CG_GetCGT          0x140032660  
#define World2Screen       0x1400C9C70
#define DrawEngineText     0x140405290
#define GetFont            0x1403DCD90
#define RegisterTag        0x14031AC90
#define GetTagPos          0x1400FBDE0
#define CG_GetWeaponInfo   0x1407E1780

// Memory addresses for decryption
#define CG_POINTER_ADDRESS    0x142B7E2E0
#define ENTITY_POINTER_ADDRESS 0x142B7E1C0

// Constants
#define MAX_ENTITIES       0x700    // 1792 entities max
#define ENTITY_SIZE        0x900    // 2304 bytes per entity
#define CLIENT_INFO_SIZE   0xEB0    // Client info struct size
#define CLIENT_INFO_OFFSET 0x2E6BB4 // Offset in CG_T for client array

// Process attachment functions
DWORD GetProcessId(const std::wstring& processName) {
    DWORD processId = 0;
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    
    if (hSnapshot != INVALID_HANDLE_VALUE) {
        PROCESSENTRY32W pe32;
        pe32.dwSize = sizeof(PROCESSENTRY32W);
        
        if (Process32FirstW(hSnapshot, &pe32)) {
            do {
                if (processName == pe32.szExeFile) {
                    processId = pe32.th32ProcessID;
                    break;
                }
            } while (Process32NextW(hSnapshot, &pe32));
        }
        CloseHandle(hSnapshot);
    }
    
    return processId;
}

bool InitializeMemoryAccess() {
    std::wcout << L"Looking for Black Ops 3..." << std::endl;

    // Try multiple possible process names
    const std::wstring processNames[] = {
        L"BlackOps3.exe",
        L"t7.exe",
        L"cod.exe",
        L"bo3.exe",
        L"BlackOps3_64.exe"
    };

    for (const auto& processName : processNames) {
        std::wcout << L"Trying: " << processName << std::endl;
        g_processId = GetProcessId(processName);
        if (g_processId != 0) {
            std::wcout << L"Found BO3 process: " << processName << L" (PID: " << g_processId << L")" << std::endl;
            break;
        }
    }

    if (g_processId == 0) {
        std::wcout << L"Black Ops 3 not found! Make sure the game is running." << std::endl;
        std::wcout << L"Supported versions: Steam, Battle.net" << std::endl;
        return false;
    }
    
    g_hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, g_processId);
    if (g_hProcess == nullptr) {
        std::wcout << L"Failed to open process! Try running as administrator." << std::endl;
        return false;
    }
    
    std::wcout << L"Successfully attached to Black Ops 3 (PID: " << g_processId << L")" << std::endl;
    return true;
}

// Decryption functions (simplified versions - may need adjustment)
uintptr_t DecryptCGPtr(uintptr_t ptr) {
    // Simplified decryption - in reality this is more complex
    // For now, we'll try to use the pointer directly and see if it works
    return ptr;
}

uintptr_t DecryptCEntityPtr(uintptr_t ptr) {
    // Simplified decryption - in reality this is more complex
    // For now, we'll try to use the pointer directly and see if it works
    return ptr;
}

// Helper functions
CG_T* GetCGT() {
    typedef CG_T*(*CG_GetCGT_t)(int);
    CG_GetCGT_t getCGT = (CG_GetCGT_t)CG_GetCGT;
    return getCGT(0);
}

CEntity* GetLocalPlayer() {
    CG_T* cgt = GetCGT();
    if (!cgt) return nullptr;

    int localIndex = ReadMemory<int>((uintptr_t)cgt);

    typedef CEntity*(*CG_GetEntity_t)(int, int);
    CG_GetEntity_t getEntity = (CG_GetEntity_t)CG_GetEntity;

    return getEntity(0, localIndex);
}

CClientInfo* GetLocalClientInfo() {
    CG_T* cgt = GetCGT();
    if (!cgt) return nullptr;

    int localIndex = ReadMemory<int>((uintptr_t)cgt);
    return (CClientInfo*)((uintptr_t)cgt + CLIENT_INFO_OFFSET + (localIndex * CLIENT_INFO_SIZE) - 0x2C);
}

// Test function to verify our setup works
bool TestMemoryAccess() {
    std::wcout << L"\n=== Testing Memory Access ===" << std::endl;

    // Test 1: Get CG_T pointer
    CG_T* cgt = GetCGT();
    if (!cgt) {
        std::wcout << L"❌ Failed to get CG_T pointer" << std::endl;
        return false;
    }
    std::wcout << L"✅ CG_T pointer: 0x" << std::hex << (uintptr_t)cgt << std::dec << std::endl;

    // Test 2: Get local player
    CEntity* localPlayer = GetLocalPlayer();
    if (!localPlayer) {
        std::wcout << L"❌ Failed to get local player" << std::endl;
        return false;
    }
    std::wcout << L"✅ Local player pointer: 0x" << std::hex << (uintptr_t)localPlayer << std::dec << std::endl;

    // Test 3: Read player position
    Vec3 playerPos = ReadMemory<Vec3>((uintptr_t)localPlayer + 0x40);
    std::wcout << L"✅ Player position: X=" << playerPos.x << L" Y=" << playerPos.y << L" Z=" << playerPos.z << std::endl;

    // Test 4: Get client info
    CClientInfo* clientInfo = GetLocalClientInfo();
    if (!clientInfo) {
        std::wcout << L"❌ Failed to get client info" << std::endl;
        return false;
    }

    char playerName[17] = {0};
    ReadProcessMemory(g_hProcess, (LPCVOID)((uintptr_t)clientInfo + 0x0C), playerName, 16, nullptr);
    std::wcout << L"✅ Player name: " << playerName << std::endl;

    WORD weaponIndex = ReadMemory<WORD>((uintptr_t)clientInfo + 0x6F8);
    std::wcout << L"✅ Current weapon index: " << weaponIndex << std::endl;

    return true;
}

// Overpowered Weapon Modifier Class
class BO3WeaponMods {
private:
    bool weaponModsEnabled = false;
    std::map<WORD, CG_WeaponInfo> originalWeaponStats;

public:
    void ToggleWeaponMods() {
        weaponModsEnabled = !weaponModsEnabled;

        if (weaponModsEnabled) {
            std::wcout << L"\n🔥 OVERPOWERED WEAPONS: ENABLED 🔥" << std::endl;
            ApplyWeaponMods();
        } else {
            std::wcout << L"\n❌ OVERPOWERED WEAPONS: DISABLED" << std::endl;
            RestoreWeaponMods();
        }
    }

    bool IsEnabled() const { return weaponModsEnabled; }

private:
    void ApplyWeaponMods() {
        std::wcout << L"Modifying weapons..." << std::endl;

        // Get current weapon first
        CClientInfo* clientInfo = GetLocalClientInfo();
        if (!clientInfo) {
            std::wcout << L"❌ Failed to get client info" << std::endl;
            return;
        }

        WORD currentWeapon = ReadMemory<WORD>((uintptr_t)clientInfo + 0x6F8);
        std::wcout << L"Current weapon index: " << currentWeapon << std::endl;

        // Modify current weapon and common weapon indices
        std::vector<WORD> weaponsToModify = {currentWeapon};

        // Add common weapon indices (0-50 should cover most weapons)
        for (WORD i = 0; i < 50; i++) {
            weaponsToModify.push_back(i);
        }

        int modifiedCount = 0;
        for (WORD weaponIndex : weaponsToModify) {
            if (ModifyWeapon(weaponIndex)) {
                modifiedCount++;
            }
        }

        std::wcout << L"✅ Modified " << modifiedCount << L" weapons" << std::endl;
    }

    bool ModifyWeapon(WORD weaponIndex) {
        typedef CG_WeaponInfo*(*CG_GetWeaponInfo_t)(WORD);
        CG_GetWeaponInfo_t getWeaponInfo = (CG_GetWeaponInfo_t)CG_GetWeaponInfo;

        CG_WeaponInfo* weaponInfo = getWeaponInfo(weaponIndex);
        if (!weaponInfo) return false;

        uintptr_t weaponAddr = (uintptr_t)weaponInfo;

        // Store original stats if not already stored
        if (originalWeaponStats.find(weaponIndex) == originalWeaponStats.end()) {
            originalWeaponStats[weaponIndex] = ReadMemory<CG_WeaponInfo>(weaponAddr);
        }

        // Apply INSANE modifications
        WriteMemory<float>(weaponAddr + 0x10, 999999.0f);    // Insane damage
        WriteMemory<float>(weaponAddr + 0x14, 0.001f);       // Super fast fire rate
        WriteMemory<float>(weaponAddr + 0x18, 0.1f);         // Fast reload
        WriteMemory<float>(weaponAddr + 0x1C, 0.01f);        // Instant ADS
        WriteMemory<float>(weaponAddr + 0x20, 0.01f);        // Instant ready
        WriteMemory<float>(weaponAddr + 0x24, 0.0f);         // Perfect hip accuracy
        WriteMemory<float>(weaponAddr + 0x28, 0.0f);         // Perfect ADS accuracy
        WriteMemory<float>(weaponAddr + 0x2C, 0.0f);         // No vertical recoil
        WriteMemory<float>(weaponAddr + 0x30, 0.0f);         // No horizontal recoil
        WriteMemory<int>(weaponAddr + 0x34, 9999);           // Huge clip
        WriteMemory<int>(weaponAddr + 0x38, 99999);          // Massive reserve
        WriteMemory<float>(weaponAddr + 0x3C, 999999.0f);    // Infinite range
        WriteMemory<float>(weaponAddr + 0x40, 999.0f);       // Max penetration
        WriteMemory<float>(weaponAddr + 0x44, 500.0f);       // Huge explosion radius
        WriteMemory<float>(weaponAddr + 0x48, 999999.0f);    // Insane explosion damage

        return true;
    }

    void RestoreWeaponMods() {
        std::wcout << L"Restoring original weapon stats..." << std::endl;

        for (auto& pair : originalWeaponStats) {
            WORD weaponIndex = pair.first;
            CG_WeaponInfo originalStats = pair.second;

            typedef CG_WeaponInfo*(*CG_GetWeaponInfo_t)(WORD);
            CG_GetWeaponInfo_t getWeaponInfo = (CG_GetWeaponInfo_t)CG_GetWeaponInfo;

            CG_WeaponInfo* weaponInfo = getWeaponInfo(weaponIndex);
            if (weaponInfo) {
                WriteMemory<CG_WeaponInfo>((uintptr_t)weaponInfo, originalStats);
            }
        }

        originalWeaponStats.clear();
        std::wcout << L"✅ Weapons restored to normal" << std::endl;
    }
};

// Infinite Ammo Class
class BO3AmmoMods {
private:
    bool infiniteAmmoEnabled = false;

public:
    void ToggleInfiniteAmmo() {
        infiniteAmmoEnabled = !infiniteAmmoEnabled;
        std::wcout << L"\n🔫 INFINITE AMMO: " << (infiniteAmmoEnabled ? L"ENABLED" : L"DISABLED") << std::endl;
    }

    void Update() {
        if (!infiniteAmmoEnabled) return;

        ApplyInfiniteAmmo();
    }

    bool IsEnabled() const { return infiniteAmmoEnabled; }

private:
    void ApplyInfiniteAmmo() {
        CClientInfo* clientInfo = GetLocalClientInfo();
        if (!clientInfo) return;

        WORD weaponIndex = ReadMemory<WORD>((uintptr_t)clientInfo + 0x6F8);

        typedef CG_WeaponInfo*(*CG_GetWeaponInfo_t)(WORD);
        CG_GetWeaponInfo_t getWeaponInfo = (CG_GetWeaponInfo_t)CG_GetWeaponInfo;

        CG_WeaponInfo* weaponInfo = getWeaponInfo(weaponIndex);
        if (weaponInfo) {
            // Keep ammo at maximum
            WriteMemory<int>((uintptr_t)weaponInfo + 0x34, 9999);  // Current clip
            WriteMemory<int>((uintptr_t)weaponInfo + 0x38, 99999); // Reserve ammo
        }
    }
};

// Player Modifications Class
class BO3PlayerMods {
private:
    bool playerModsEnabled = false;

public:
    void TogglePlayerMods() {
        playerModsEnabled = !playerModsEnabled;
        std::wcout << L"\n💪 PLAYER MODS: " << (playerModsEnabled ? L"ENABLED" : L"DISABLED") << std::endl;
    }

    void Update() {
        if (!playerModsEnabled) return;

        CEntity* localPlayer = GetLocalPlayer();
        if (!localPlayer) return;

        ApplyPlayerMods(localPlayer);
    }

    bool IsEnabled() const { return playerModsEnabled; }

private:
    void ApplyPlayerMods(CEntity* player) {
        uintptr_t playerAddr = (uintptr_t)player;

        // Try to set infinite health at common offsets
        WriteMemory<int>(playerAddr + 0x100, 999999);  // Try offset 0x100
        WriteMemory<int>(playerAddr + 0x150, 999999);  // Try offset 0x150
        WriteMemory<int>(playerAddr + 0x200, 999999);  // Try offset 0x200

        // Remove death flags
        int32_t flags1 = ReadMemory<int32_t>(playerAddr + 0x5A0);
        int32_t flags2 = ReadMemory<int32_t>(playerAddr + 0x678);

        flags1 &= ~0x4000;  // Remove death flag
        flags2 &= ~0x4000;

        WriteMemory<int32_t>(playerAddr + 0x5A0, flags1);
        WriteMemory<int32_t>(playerAddr + 0x678, flags2);
    }
};

// Main Application Class
class BO3ZombiesMods {
private:
    BO3WeaponMods weaponMods;
    BO3AmmoMods ammoMods;
    BO3PlayerMods playerMods;
    bool running = true;

public:
    void Run() {
        std::wcout << L"\n" << std::endl;
        std::wcout << L"╔══════════════════════════════════════╗" << std::endl;
        std::wcout << L"║     BO3 ZOMBIES OVERPOWERED MOD      ║" << std::endl;
        std::wcout << L"╚══════════════════════════════════════╝" << std::endl;
        std::wcout << L"" << std::endl;
        std::wcout << L"Controls:" << std::endl;
        std::wcout << L"F1 - Toggle Overpowered Weapons 🔥" << std::endl;
        std::wcout << L"F2 - Toggle Player Mods (Health/Speed) 💪" << std::endl;
        std::wcout << L"F3 - Toggle Infinite Ammo 🔫" << std::endl;
        std::wcout << L"F5 - ENABLE ALL MODS (GODMODE) ⚡" << std::endl;
        std::wcout << L"F9 - Test Memory Access 🔧" << std::endl;
        std::wcout << L"ESC - Exit" << std::endl;
        std::wcout << L"" << std::endl;

        while (running) {
            HandleInput();

            // Update continuous features
            ammoMods.Update();
            playerMods.Update();

            Sleep(10);
        }

        std::wcout << L"\nShutting down..." << std::endl;
        if (g_hProcess) {
            CloseHandle(g_hProcess);
        }
    }

private:
    void HandleInput() {
        if (GetAsyncKeyState(VK_F1) & 0x8000) {
            weaponMods.ToggleWeaponMods();
            Sleep(200);
        }

        if (GetAsyncKeyState(VK_F2) & 0x8000) {
            playerMods.TogglePlayerMods();
            Sleep(200);
        }

        if (GetAsyncKeyState(VK_F3) & 0x8000) {
            ammoMods.ToggleInfiniteAmmo();
            Sleep(200);
        }

        if (GetAsyncKeyState(VK_F5) & 0x8000) {
            std::wcout << L"\n⚡⚡⚡ GODMODE ACTIVATED ⚡⚡⚡" << std::endl;
            std::wcout << L"Enabling all mods..." << std::endl;

            if (!weaponMods.IsEnabled()) weaponMods.ToggleWeaponMods();
            if (!playerMods.IsEnabled()) playerMods.TogglePlayerMods();
            if (!ammoMods.IsEnabled()) ammoMods.ToggleInfiniteAmmo();

            std::wcout << L"🎮 You are now UNSTOPPABLE! 🎮" << std::endl;
            Sleep(500);
        }

        if (GetAsyncKeyState(VK_F9) & 0x8000) {
            std::wcout << L"\n🔧 Running memory access test..." << std::endl;
            TestMemoryAccess();
            Sleep(200);
        }

        if (GetAsyncKeyState(VK_ESCAPE) & 0x8000) {
            running = false;
        }
    }

    void ShowStatus() {
        std::wcout << L"\n=== Current Status ===" << std::endl;
        std::wcout << L"Weapons: " << (weaponMods.IsEnabled() ? L"OVERPOWERED ✅" : L"Normal ❌") << std::endl;
        std::wcout << L"Player: " << (playerMods.IsEnabled() ? L"ENHANCED ✅" : L"Normal ❌") << std::endl;
        std::wcout << L"Ammo: " << (ammoMods.IsEnabled() ? L"INFINITE ✅" : L"Normal ❌") << std::endl;
    }
};

// Main entry point
int main() {
    // Set console to handle Unicode
    SetConsoleOutputCP(CP_UTF8);

    std::wcout << L"Starting BO3 Zombies Overpowered Mod..." << std::endl;

    // Initialize memory access
    if (!InitializeMemoryAccess()) {
        std::wcout << L"\n❌ Failed to initialize! Make sure:" << std::endl;
        std::wcout << L"1. Black Ops 3 is running" << std::endl;
        std::wcout << L"2. You're running this as Administrator" << std::endl;
        std::wcout << L"3. The game version is ********" << std::endl;
        std::wcout << L"\nPress any key to exit..." << std::endl;
        std::cin.get();
        return 1;
    }

    // Run the mod
    BO3ZombiesMods mods;
    mods.Run();

    return 0;
}
