@echo off
echo ========================================
echo    BO3 Zombies Overpowered Mod
echo    Compilation Script
echo ========================================
echo.

REM Try different compiler paths
where cl >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo Setting up compiler environment...

    REM Try Visual Studio 2022 Community
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
    if %ERRORLEVEL% NEQ 0 (
        REM Try Visual Studio 2022 Professional
        call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" 2>nul
        if %ERRORLEVEL% NEQ 0 (
            REM Try Visual Studio 2019
            call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" 2>nul
            if %ERRORLEVEL% NEQ 0 (
                REM Try Build Tools
                call "C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\VC\Auxiliary\Build\vcvars64.bat" 2>nul
                if %ERRORLEVEL% NEQ 0 (
                    REM Try Windows SDK
                    call "C:\Program Files (x86)\Windows Kits\10\bin\10.0.22621.0\x64\rc.exe" >nul 2>nul
                    if %ERRORLEVEL% NEQ 0 (
                        echo.
                        echo ERROR: No C++ compiler found!
                        echo.
                        echo Please install one of:
                        echo 1. Visual Studio 2019/2022 with C++ support
                        echo 2. Visual Studio Build Tools
                        echo 3. Windows SDK
                        echo.
                        echo Or try using an online compiler like:
                        echo - replit.com
                        echo - onlinegdb.com
                        echo.
                        pause
                        exit /b 1
                    )
                )
            )
        )
    )
)

echo Compiling BO3_Mods.cpp...
cl /EHsc /O2 /MT BO3_Mods.cpp /Fe:BO3_ZombiesMod.exe

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo    COMPILATION SUCCESSFUL!
    echo ========================================
    echo.
    echo Output: BO3_ZombiesMod.exe
    echo.
    echo Instructions:
    echo 1. Start Black Ops 3
    echo 2. Load into a Zombies map
    echo 3. Run BO3_ZombiesMod.exe as Administrator
    echo 4. Use F1-F5 keys to activate mods
    echo.
    echo WARNING: Use at your own risk!
    echo This is for educational purposes only.
    echo.
) else (
    echo.
    echo ========================================
    echo    COMPILATION FAILED!
    echo ========================================
    echo.
    echo Check the error messages above.
    echo Make sure you have Visual Studio installed.
    echo.
)

pause
