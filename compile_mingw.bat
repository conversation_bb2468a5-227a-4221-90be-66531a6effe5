@echo off
echo ========================================
echo    BO3 Zombies Overpowered Mod
echo    MinGW Compilation Script
echo ========================================
echo.

REM Check if MinGW is available
where g++ >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo MinGW not found in PATH.
    echo.
    echo Trying common MinGW locations...
    
    if exist "C:\MinGW\bin\g++.exe" (
        set PATH=C:\MinGW\bin;%PATH%
        echo Found MinGW at C:\MinGW\bin
    ) else if exist "C:\msys64\mingw64\bin\g++.exe" (
        set PATH=C:\msys64\mingw64\bin;%PATH%
        echo Found MinGW at C:\msys64\mingw64\bin
    ) else if exist "C:\TDM-GCC-64\bin\g++.exe" (
        set PATH=C:\TDM-GCC-64\bin;%PATH%
        echo Found TDM-GCC at C:\TDM-GCC-64\bin
    ) else (
        echo.
        echo ERROR: MinGW/GCC not found!
        echo.
        echo Please install MinGW-w64 or TDM-GCC:
        echo 1. Download from: https://www.mingw-w64.org/downloads/
        echo 2. Or install MSYS2: https://www.msys2.org/
        echo 3. Add to PATH environment variable
        echo.
        pause
        exit /b 1
    )
)

echo Compiling with MinGW...
g++ -std=c++17 -O2 -static -static-libgcc -static-libstdc++ BO3_Mods.cpp -o BO3_ZombiesMod.exe

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo    COMPILATION SUCCESSFUL!
    echo ========================================
    echo.
    echo Output: BO3_ZombiesMod.exe
    echo.
    echo Instructions:
    echo 1. Start Black Ops 3
    echo 2. Load into a Zombies map
    echo 3. Run BO3_ZombiesMod.exe as Administrator
    echo 4. Use F1-F5 keys to activate mods
    echo.
    echo WARNING: Use at your own risk!
    echo This is for educational purposes only.
    echo.
) else (
    echo.
    echo ========================================
    echo    COMPILATION FAILED!
    echo ========================================
    echo.
    echo Check the error messages above.
    echo Make sure MinGW is properly installed.
    echo.
)

pause
