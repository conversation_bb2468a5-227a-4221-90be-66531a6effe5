# Black Ops 3 Zombies Overpowered Mod

A powerful external mod for Call of Duty: Black Ops 3 Zombies that makes weapons absolutely overpowered and adds various enhancements.

## ⚠️ DISCLAIMER
This is for **educational purposes only**. Use at your own risk. This may violate the game's Terms of Service.

## 🎮 Features

### 🔥 Overpowered Weapons (F1)
- **999,999 damage** - One-shot kill everything
- **0.001s fire rate** - Nearly full-auto everything  
- **0.1s reload** - Lightning fast reloads
- **Perfect accuracy** - No spread at all
- **No recoil** - Laser beam weapons
- **9,999 clip size** - Massive magazines
- **99,999 reserve ammo** - Never run out
- **Infinite range** - No damage falloff
- **Wall penetration** - Shoot through everything
- **Explosive rounds** - Everything explodes

### 💪 Player Mods (F2)
- **Infinite health** - Never die (attempts multiple health offsets)
- **Death immunity** - Remove death flags
- **Enhanced survivability**

### 🔫 Infinite Ammo (F3)
- **Never reload** - Constant ammo refill
- **Auto-refill** - Keeps ammo topped off

### ⚡ Godmode (F5)
- **Enables ALL mods** at once
- **Ultimate power** - Become unstoppable

## 🛠️ Requirements

- **Black Ops 3** (Version ********)
- **Windows 10/11**
- **Visual Studio 2019/2022** with C++ support
- **Administrator privileges**

## 📦 Installation & Usage

### Step 1: Compile the Mod
1. **Double-click `compile.bat`**
2. Wait for compilation to complete
3. You should see `BO3_ZombiesMod.exe` created

### Step 2: Run the Mod
1. **Start Black Ops 3**
2. **Load into a Zombies map**
3. **Right-click `BO3_ZombiesMod.exe`** → **"Run as Administrator"**
4. You should see the mod menu appear

### Step 3: Use the Mods
- **F1** - Toggle Overpowered Weapons 🔥
- **F2** - Toggle Player Mods 💪  
- **F3** - Toggle Infinite Ammo 🔫
- **F5** - Enable ALL mods (Godmode) ⚡
- **F9** - Test memory access 🔧
- **ESC** - Exit

## 🔧 Troubleshooting

### "Black Ops 3 not found!"
- Make sure the game is running
- Check that the process name is "BlackOps3.exe"

### "Failed to open process!"
- **Run as Administrator** (very important!)
- Disable antivirus temporarily
- Make sure no other memory tools are running

### "Memory access test failed"
- Game version might be different (needs ********)
- Offsets may need adjustment
- Try restarting both game and mod

### Compilation Errors
- Install **Visual Studio 2019 or 2022**
- Make sure **C++ development tools** are installed
- Try running `compile.bat` as Administrator

## 📋 Technical Details

### Memory Offsets (Version ********)
```cpp
CG_GetEntity        = 0x140032530
CG_GetCGT          = 0x140032660  
CG_GetWeaponInfo   = 0x1407E1780
World2Screen       = 0x1400C9C70
```

### Weapon Modifications
The mod modifies the `CG_WeaponInfo` structure:
- Damage at offset 0x10
- Fire delay at offset 0x14
- Reload time at offset 0x18
- Accuracy values at offsets 0x24-0x28
- Ammo values at offsets 0x34-0x38

## ⚠️ Important Notes

1. **Game Version**: Only works with BO3 version ********
2. **Administrator**: Must run as Administrator
3. **Antivirus**: May be flagged as suspicious (false positive)
4. **Online Play**: Do NOT use in online matches
5. **Backups**: Game will return to normal when mod is disabled

## 🎯 What This Does in Game

When you activate the mods:
- **Any weapon** becomes a one-shot kill monster
- **Pistols** become more powerful than rocket launchers  
- **SMGs** fire like miniguns with perfect accuracy
- **Snipers** become explosive full-auto cannons
- **You become nearly invincible**
- **Zombies become trivial** - you're a walking apocalypse

## 🔄 Updates

If the game updates and breaks the mod:
1. Check for new offsets on UnknownCheats forum
2. Update the `#define` addresses in the code
3. Recompile with `compile.bat`

## 📞 Support

If you need help:
1. Make sure you followed all steps exactly
2. Check the troubleshooting section
3. Verify your game version is ********
4. Try the F9 memory test to see what's working

---

**Remember: This is for educational purposes only. Use responsibly!**
