@echo off
title BO3 Zombies Overpowered Mod Launcher
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                BO3 ZOMBIES OVERPOWERED MOD                  ║
echo ║                      LAUNCHER v1.0                          ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🎮 Checking for Black Ops 3...
tasklist | findstr -i "BlackOps3.exe" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Black Ops 3 is running!
    echo.
    echo 🚀 Starting the overpowered mod...
    echo.
    echo ⚠️  IMPORTANT: This will run as Administrator
    echo    Click "Yes" when Windows asks for permission
    echo.
    pause
    
    REM Run as administrator
    powershell -Command "Start-Process '.\BO3_ZombiesMod.exe' -Verb RunAs"
    
    echo.
    echo ✅ Mod launched! Check the mod window for controls.
    echo.
    echo 🎯 Controls:
    echo    F1 - Overpowered Weapons 🔥
    echo    F2 - Player Mods 💪
    echo    F3 - Infinite Ammo 🔫
    echo    F5 - GODMODE (All Mods) ⚡
    echo    F9 - Test Memory Access 🔧
    echo    ESC - Exit
    echo.
    
) else (
    echo ❌ Black Ops 3 is not running!
    echo.
    echo 📋 Instructions:
    echo 1. Start Black Ops 3
    echo 2. Go to Zombies mode
    echo 3. Load any zombies map
    echo 4. Get in-game and spawned
    echo 5. Run this launcher again
    echo.
    echo 💡 Tip: Make sure you're using BO3 version ********
    echo.
)

echo Press any key to exit...
pause >nul
